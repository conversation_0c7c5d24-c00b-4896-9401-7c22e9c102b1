

import { EmblaOptionsType } from 'embla-carousel'
import EmblaCarousel from './Emblacarousel_Hero'
const OPTIONS: EmblaOptionsType = { loop: true }
const SLIDE_COUNT = 5
const SLIDES = Array.from(Array(SLIDE_COUNT).keys())



const HeroSlideshow = () => {
  return (
    <div className="overflow-hidden">
      <EmblaCarousel slides={SLIDES} options={OPTIONS} />
    </div>
  )
}

export default HeroSlideshow
