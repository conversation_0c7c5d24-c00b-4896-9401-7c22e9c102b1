import { enTranslations } from "@payloadcms/translations/languages/en";
import type { NestedKeysStripped } from "@payloadcms/translations";

export const customTranslations = {
  zh: {
    custom: {
      user: "账户",
      media: "媒体",
      new:"新闻"
    },
    general: {
      locale: "数据多语言环境",
      locales: "数据多语言环境",
    },
  },

  en: {
    custom: {
      user: "User",
      media: "Media",
      new:"News"
    },
  },
};

export type CustomTranslationsObject = typeof customTranslations.en &
  typeof customTranslations.zh &
  typeof enTranslations;
export type CustomTranslationsKeys =
  NestedKeysStripped<CustomTranslationsObject>;
