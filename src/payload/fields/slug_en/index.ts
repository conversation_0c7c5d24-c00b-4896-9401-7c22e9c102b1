import type { CheckboxField, TextField } from "payload";

import { formatSlugHook } from "../slug/formatSlug";

type Overrides = {
  slugOverrides?: Partial<TextField>;
  checkboxOverrides?: Partial<CheckboxField>;
};

type Slug = (
  fieldToUse?: string,
  overrides?: Overrides
) => [TextField, CheckboxField];

export const slugField: Slug = (fieldToUse = "title", overrides = {}) => {
  const { slugOverrides, checkboxOverrides } = overrides;

  //判断当前语言环境

  const checkBoxField: CheckboxField = {
    name: "slugLock",
    type: "checkbox",
    defaultValue: true,
    admin: {
      hidden: true,
      position: "sidebar",
    },
    ...checkboxOverrides,
  };

  // @ts-expect-error - ts mismatch Partial<TextField> with TextField
  const slugField: TextField = {
    name: "slug",
    label: {
      en: "slug",
      zh: "链接别名",
    },
    type: "text",
    index: true,

    ...(slugOverrides || {}),
    hooks: {
      // Kept this in for hook or API based updates

      beforeValidate: [formatSlugHook(fieldToUse)],
      afterChange: [formatSlugHook(fieldToUse)],
    },
    admin: {
      position: "sidebar",
      ...(slugOverrides?.admin || {}),
    },
  };

  return [slugField, checkBoxField];
};
