import type { CollectionConfig } from "payload";

import { slugField } from "@payload/fields/slug_en";

import slugify from "slugify";

const Product: CollectionConfig = {
  slug: "products",

  admin: {
    useAsTitle: "name", // 使用虚拟字段 "fullName" 作为标题
    group: "商品管理",
    description: "产品管理",
    defaultColumns: ["name", "categories", "updatedAt"],
  },

  defaultPopulate: {
    name: true,
    slug: true,
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: "name",
      label: {
        en: "name",
        zh: "名称",
      },
      type: "text",
      required: true,
      localized: true,
      admin: {
        style: {
          width: "50%",
        },
      },
    },

    {
      name: "description",
      type: "textarea",
      label: {
        en: "description",
        zh: "描述",
      },
      required: false,
      localized: true,
    },

    {
      name: "categories",
      label: {
        en: "categories",
        zh: "分类",
      },
      type: "relationship",
      relationTo: "categories",
      hasMany: true,
    },
    {
      name: "price",
      label: {
        en: "price",
        zh: "价格",
      },
      type: "number",
      required: false,
    },
    {
      name: "image",
      label: {
        en: "image",
        zh: "图片",
      },
      type: "upload",
      relationTo: "media",
      hasMany: true,
      admin: {
        position: "sidebar",
      },
    },
    {
      name: "specs",
      type: "array",
      fields: [
        {
          name: "label",
          type: "text",
          label: {
            en: "label",
            zh: "参数名",
          },
        },
        {
          name: "value",
          type: "text",
          label: {
            en: "value",
            zh: "值",
          },
        },
      ],
    },

  

    {
      name: "slug",
      type: "text",
      label: "链接别名",
      required: false,
      admin: {
        position: "sidebar",
        readOnly: true, // 默认设置为只读，防止手动更改
        description:"此链接根据 产品名称 自动生成，不可手动更改",
     
      },
    },

    // ...slugField("name"),
    // ... 其他字段 ...
  ],

  hooks: {
    beforeValidate: [
      ({ data, req: { locale } }) => {
        // 只在默认语言或 en 状态下生成 slug
            if (locale === "en") {
                data.slug = slugify(data.name, {
                  lower: true,
                  strict: true,
                });
        }       
        return data;
      },
    ],
  },
  // ... 其他配置 ...
};

export { Product };
