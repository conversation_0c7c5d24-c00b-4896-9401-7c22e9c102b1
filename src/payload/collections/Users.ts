import type { CollectionConfig } from 'payload'
// import type {
//   TFunction,
// } from '@payloadcms/translations'



// import { CustomTranslationsKeys } from '@payload/custom-translations'


export const Users: CollectionConfig = {
  slug: 'users',
//   labels: {
//     singular: ({ t: defaultT }) => {
//       const t = defaultT as TFunction<CustomTranslationsKeys>
//       return t('custom:user')
//     },
//     plural:  ({ t: defaultT }) => {
//       const t = defaultT as TFunction<CustomTranslationsKeys>
//       return t('custom:user')
//     },

//   },
  admin: {
    useAsTitle: 'email',
  },
  auth: true,
  fields: [
    // Email added by default
    // Add more fields as needed
  ],
}
