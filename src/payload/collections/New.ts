import type { CollectionConfig } from "payload";
// import type {
//   TFunction,
// } from '@payloadcms/translations'

// import { CustomTranslationsKeys } from '@payload/custom-translations'

export const New: CollectionConfig = {
  slug: "news",
  //   labels: {
  //     singular: ({ t: defaultT }) => {
  //       const t = defaultT as TFunction<CustomTranslationsKeys>
  //       return t('custom:new')
  //     },
  //     plural: ({ t: defaultT }) => {
  //       const t = defaultT as TFunction<CustomTranslationsKeys>
  //       return t('custom:new')
  //     },
  //   },
  admin: {
    useAsTitle: "title", // 使用虚拟字段 "fullName" 作为标题
    description: "新闻管理",
    group: "文章",
  },
  access: {
    read: () => true,
  },
  fields: [
    {
      name: "title",
      label: "标题",
      type: "text",
      localized: true,
      required: true,
    },

    {
      name: "description",
      label: "描述",
      type: "text",
      localized: true,
      required: false,
    },
    {
      name: "content",
      label: "内容",
      type: "richText",
      localized: true,
      required: false,
    },
    {
      name: "cover",
      label: "封面",
      type: "upload",
      relationTo: "media",
      required: false,
      admin: {
        position: "sidebar",
      },
    },
    {
      name: "publishTime",
      type: "date",
      label: "发布时间",
      required: false,
      admin: {
        description: "文章发布时间",
        date: {
          pickerAppearance: "dayAndTime",
          displayFormat: "yyyy-MM-dd HH:mm",
        },
        position: "sidebar",
      },
    },

    {
      name: "tags",
      label: "标签",
      type: "array",

      localized: true,
      required: false,
      fields: [
        {
          type: "text",
          name: "tag",
          label: "标签项",
        },
      ],
      admin: {

        width: "50%",
      },
    },

    // ... 其他字段 ...
  ],
  // ... 其他配置 ...
};
