// storage-adapter-import-placeholder
import { postgresAdapter } from "@payloadcms/db-postgres";
// import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { vercelBlobStorage } from "@payloadcms/storage-vercel-blob";
// import fs from "fs";
import { lexicalEditor } from "@payloadcms/richtext-lexical";
import path from "path";
import { buildConfig } from "payload";
import { fileURLToPath } from "url";
import sharp from "sharp";
import { SharpDependency } from "payload";
import { resendAdapter } from '@payloadcms/email-resend'
import { en } from "@payloadcms/translations/languages/en";
import { zh } from "@payloadcms/translations/languages/zh";
import { customTranslations } from "./custom-translations";
import { Users } from "./collections/Users";
import { Media } from "./collections/Media";
import { Product } from "./collections/Porduct";
import { Category } from "./collections/Category";
import { Header } from "./collections/Header";
import { Specs } from "./collections/Specs";
import { New } from "./collections/New";

const filename = fileURLToPath(import.meta.url);
const dirname = path.dirname(filename);


export default buildConfig({
  i18n: {
    supportedLanguages: { en, zh },
    translations: customTranslations,
    // ...
  },
  localization: {
    locales: ["en", "zh"],
    defaultLocale: "zh",
    fallback: true,
  },
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  collections: [Users, Media, Product, Category, Specs,New],
  globals: [Header],

  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || "",
  typescript: {
    outputFile: path.resolve(dirname, "payload-types.ts"),
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || "",
      ssl: {
        rejectUnauthorized: false,
        // ca: fs.readFileSync(process.env.SSL_CERT_PATH || "").toString(),
      },
    },
  }),
  sharp: sharp as unknown as SharpDependency,

  plugins: [
    // payloadCloudPlugin(),
    // storage-adapter-placeholder
    vercelBlobStorage({
      enabled: true, // Optional, defaults to true
      // Specify which collections should use Vercel Blob
      collections: {
        media: true,
        // 'media-with-prefix': {
        //   prefix: 'xgs-',
        // },
      },
      // Token provided by Vercel once Blob storage is added to your Vercel project
      token: process.env.BLOB_READ_WRITE_TOKEN,
    }),
  ],

  email: resendAdapter({
    defaultFromAddress: '<EMAIL>',
    defaultFromName: 'xgs cms',
    apiKey: process.env.RESEND_API_KEY || '',
  }),
});
