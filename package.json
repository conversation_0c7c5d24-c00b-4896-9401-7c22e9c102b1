{"name": "cue", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "generate:importmap": "payload generate:importmap", "payload": " payload"}, "dependencies": {"@next/bundle-analyzer": "^15.3.4", "@number-flow/react": "^0.5.10", "@payloadcms/db-postgres": "^3.43.0", "@payloadcms/email-resend": "^3.43.0", "@payloadcms/next": "^3.43.0", "@payloadcms/payload-cloud": "^3.43.0", "@payloadcms/richtext-lexical": "^3.43.0", "@payloadcms/storage-vercel-blob": "^3.43.0", "@payloadcms/translations": "^3.43.0", "@payloadcms/ui": "^3.43.0", "@portabletext/react": "^3.2.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toggle": "^1.1.9", "@react-icons/all-files": "^4.1.0", "@tanstack/react-query": "^5.81.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel": "^8.6.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "graphql": "^16.11.0", "lucide-react": "^0.483.0", "motion": "^12.18.1", "next": "15.2.3", "next-intl": "^4.1.0", "next-themes": "^0.4.6", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-use": "^17.6.0", "sass": "^1.89.2", "sharp": "^0.34.2", "slugify": "^1.6.6", "tailwind-merge": "^3.3.1", "tw-animate-css": "^1.3.4", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@tailwindcss/postcss": "^4.1.10", "@types/node": "^20.19.1", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "eslint": "^9.29.0", "eslint-config-next": "15.2.3", "payload": "^3.43.0", "postcss": "^8.5.6", "prettier": "^3.6.0", "tailwindcss": "^4.1.10", "typescript": "^5.8.3"}}