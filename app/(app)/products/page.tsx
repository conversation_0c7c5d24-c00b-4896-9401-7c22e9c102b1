import { use } from "react";
import { payload } from "@payload/fetch";
import { getLocale } from "next-intl/server";

import { unstable_cache } from "next/cache";

type SearchParams = {
  category?: string; // 可选的类别参数
  // 其他参数...
};

export default function DdPage({
  searchParams,
}: {
  searchParams: Promise<SearchParams>;
}) {
  // 使用 use 函数解析 Promise
  const locale = use(getLocale());

  const resolvedParams = use(searchParams);
  const { category } = resolvedParams;

  const getProducts = unstable_cache(
    async () => {
      console.log("Fetching products...");
      const products = await payload.find({
        collection: "products",
        locale: locale as "en" | "zh",
        
      });
      return products;
    },
    ["products" + locale],
    { revalidate: 20 }
  );

  const products = use(getProducts());
  return (
    <section className=" min-h-screen  py-16 md:py-32">
      <div className="container mx-auto">
        <div>ID: {category}</div>

        <div> {JSON.stringify(products)}</div>
      </div>
    </section>
  );
}
