import "@/style/embla.css";
import "@/style/globals.css";
import "@/style/theme.css";
import "@/style/custom.css";



import type { Metadata } from "next";
import { NextIntlClientProvider } from "next-intl";
import { getLocale } from "next-intl/server";
import { ThemeProvider } from "@/components/theme-provider";

/* ----------------------------------- 组件 ----------------------------------- */

import { Geist, <PERSON>eist_<PERSON><PERSON> } from "next/font/google";
import { Footer } from "@/components/Footer";
import { Navbar } from "@/components/Navbar";

/* ----------------------------------- 配置 ----------------------------------- */

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "小怪兽 Little Monster",
  description: "小怪兽 Little Monster",
  icons: {
    icon: "/favicon.png", // 或者 "/favicon.png"
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const locale = await getLocale();

  if(locale === 'zh') {
    console.log("---- ~ locale:", locale)
  }
  // console.log("---- ~ locale:", locale)



  return (
    <html lang={locale} suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem={false}
          disableTransitionOnChange
        >
          <NextIntlClientProvider>
            <Navbar />

            {children}

            <Footer />
          </NextIntlClientProvider>
        </ThemeProvider>
      
      </body>
    </html>
  );
}
